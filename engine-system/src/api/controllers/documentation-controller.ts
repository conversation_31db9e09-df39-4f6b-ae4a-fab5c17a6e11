import { Request, Response } from 'express'
import { DocumentationService } from '../../services/documentation-service'
import { createErrorResponse } from '../utils/custom.errors'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()
const documentationService = DocumentationService.getInstance()

/**
 * Serve the main documentation index page
 */
export const getDocumentationIndex = (_req: Request, res: Response) => {
    documentationService
        .getDocumentation()
        .then((documentation) => {
            // Group files by directory for better organization
            const groupedFiles = groupFilesByDirectory(documentation.files)

            const html = generateIndexHTML(
                groupedFiles,
                documentation.lastUpdated
            )

            res.setHeader('Content-Type', 'text/html')
            res.status(200).send(html)
        })
        .catch((error) => {
            logger.error('Error serving documentation index:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Serve a specific documentation file
 */
export const getDocumentationFile = (req: Request, res: Response) => {
    // Extract file path from wildcard route
    const filePath = req.params[0] || req.path.replace('/docs/file/', '')

    if (!filePath) {
        return res.status(400).json({ error: 'File path is required' })
    }

    documentationService
        .getDocumentationFile(decodeURIComponent(filePath))
        .then((file) => {
            if (!file) {
                return res
                    .status(404)
                    .json({ error: 'Documentation file not found' })
            }

            const html = generateFileHTML(file)

            res.setHeader('Content-Type', 'text/html')
            res.status(200).send(html)
        })
        .catch((error) => {
            logger.error('Error serving documentation file:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Refresh documentation cache
 */
export const refreshDocumentation = (_req: Request, res: Response) => {
    documentationService.clearCache()
    documentationService
        .getDocumentation(true)
        .then((documentation) => {
            res.status(200).json({
                message: 'Documentation cache refreshed',
                filesCount: documentation.files.length,
                lastUpdated: documentation.lastUpdated,
            })
        })
        .catch((error) => {
            logger.error('Error refreshing documentation:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Get documentation as JSON API
 */
export const getDocumentationAPI = (_req: Request, res: Response) => {
    documentationService
        .getDocumentation()
        .then((documentation) => {
            res.status(200).json(documentation)
        })
        .catch((error) => {
            logger.error('Error serving documentation API:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Group files by their directory structure
 */
function groupFilesByDirectory(files: any[]) {
    const groups: { [key: string]: any[] } = {}

    files.forEach((file) => {
        const dir = file.path.includes('/') ? file.path.split('/')[0] : 'Root'
        if (!groups[dir]) {
            groups[dir] = []
        }
        groups[dir].push(file)
    })

    // Sort files within each group
    Object.keys(groups).forEach((key) => {
        groups[key].sort((a, b) => a.title.localeCompare(b.title))
    })

    return groups
}

/**
 * Generate HTML for the documentation index page
 */
function generateIndexHTML(
    groupedFiles: { [key: string]: any[] },
    lastUpdated: Date
): string {
    const groups = Object.keys(groupedFiles).sort()

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Engine Documentation</title>
    ${getCommonStyles()}
</head>
<body>
    <div class="container">
        <header>
            <h1>📚 Notification Engine Documentation</h1>
            <p class="subtitle">Comprehensive documentation for the notification engine system</p>
            <div class="meta">
                <span>Last updated: ${lastUpdated.toLocaleString()}</span>
                <a href="/docs/refresh" class="refresh-btn">🔄 Refresh</a>
                <a href="/docs/api" class="api-btn">📋 JSON API</a>
            </div>
        </header>

        <main>
            ${groups
                .map(
                    (group) => `
                <section class="doc-group">
                    <h2>${group === 'Root' ? '📄 Root Documentation' : `📁 ${group}`}</h2>
                    <div class="file-grid">
                        ${groupedFiles[group]
                            .map(
                                (file) => `
                            <div class="file-card">
                                <h3><a href="/docs/file/${encodeURIComponent(file.path)}">${file.title}</a></h3>
                                <p class="file-path">${file.path}</p>
                                <p class="file-modified">Modified: ${file.lastModified.toLocaleDateString()}</p>
                                <div class="file-preview">${file.htmlContent.substring(0, 200)}...</div>
                                <div class="file-actions">
                                    <a href="/docs/file/${encodeURIComponent(file.path)}" class="view-btn">📖 View</a>
                                </div>
                            </div>
                        `
                            )
                            .join('')}
                    </div>
                </section>
            `
                )
                .join('')}
        </main>

        <footer>
            <p>Generated by Notification Engine Documentation Service</p>
        </footer>
    </div>
</body>
</html>`
}

/**
 * Generate HTML for a specific documentation file
 */
function generateFileHTML(file: any): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${file.title} - Notification Engine Docs</title>
    ${getCommonStyles()}
    ${getMarkdownStyles()}
</head>
<body>
    <div class="container">
        <header>
            <nav class="breadcrumb">
                <a href="/docs">📚 Documentation</a> / <span>${file.title}</span>
            </nav>
            <div class="file-meta">
                <h1>${file.title}</h1>
                <p class="file-info">
                    <span class="file-path">📄 ${file.path}</span>
                    <span class="file-modified">🕒 Modified: ${file.lastModified.toLocaleString()}</span>
                </p>
            </div>
        </header>

        <main class="markdown-content">
            ${file.htmlContent}
        </main>

        <footer>
            <a href="/docs" class="back-btn">← Back to Documentation Index</a>
        </footer>
    </div>
</body>
</html>`
}

/**
 * Common CSS styles
 */
function getCommonStyles(): string {
    return `
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    header {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    h1 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 2.5em;
    }

    .subtitle {
        color: #7f8c8d;
        font-size: 1.2em;
        margin-bottom: 20px;
    }

    .meta {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .refresh-btn, .api-btn, .back-btn {
        background: #3498db;
        color: white;
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 5px;
        font-size: 0.9em;
        transition: background 0.3s;
    }

    .refresh-btn:hover, .api-btn:hover, .back-btn:hover {
        background: #2980b9;
    }

    .doc-group {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 25px;
    }

    .doc-group h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #ecf0f1;
    }

    .file-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .file-card {
        border: 1px solid #ecf0f1;
        border-radius: 8px;
        padding: 20px;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .file-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .file-card h3 {
        margin-bottom: 10px;
    }

    .file-card h3 a {
        color: #2c3e50;
        text-decoration: none;
    }

    .file-card h3 a:hover {
        color: #3498db;
    }

    .file-path {
        color: #7f8c8d;
        font-size: 0.9em;
        font-family: monospace;
        margin-bottom: 5px;
    }

    .file-modified {
        color: #95a5a6;
        font-size: 0.8em;
    }

    .file-preview {
        color: #7f8c8d;
        font-size: 0.9em;
        margin-top: 10px;
        line-height: 1.4;
    }

    .file-actions {
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #ecf0f1;
    }

    .view-btn {
        background: #27ae60;
        color: white;
        text-decoration: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 0.85em;
        transition: background 0.3s;
        display: inline-block;
    }

    .view-btn:hover {
        background: #229954;
        text-decoration: none;
        color: white;
    }

    .breadcrumb {
        margin-bottom: 20px;
    }

    .breadcrumb a {
        color: #3498db;
        text-decoration: none;
    }

    .file-meta h1 {
        margin-bottom: 10px;
    }

    .file-info {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        color: #7f8c8d;
        font-size: 0.9em;
    }

    footer {
        text-align: center;
        margin-top: 40px;
        padding: 20px;
        color: #95a5a6;
    }

    @media (max-width: 768px) {
        .container {
            padding: 10px;
        }

        header {
            padding: 20px;
        }

        h1 {
            font-size: 2em;
        }

        .file-grid {
            grid-template-columns: 1fr;
        }

        .meta {
            flex-direction: column;
            align-items: flex-start;
        }

        .file-info {
            flex-direction: column;
            gap: 5px;
        }
    }
</style>`
}

/**
 * Markdown-specific CSS styles
 */
function getMarkdownStyles(): string {
    return `
<style>
    .markdown-content {
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3,
    .markdown-content h4,
    .markdown-content h5,
    .markdown-content h6 {
        color: #2c3e50;
        margin-top: 30px;
        margin-bottom: 15px;
        line-height: 1.3;
    }

    .markdown-content h1 {
        font-size: 2.2em;
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
    }

    .markdown-content h2 {
        font-size: 1.8em;
        border-bottom: 2px solid #ecf0f1;
        padding-bottom: 8px;
    }

    .markdown-content h3 {
        font-size: 1.4em;
    }

    .markdown-content p {
        margin-bottom: 15px;
        text-align: justify;
    }

    .markdown-content ul,
    .markdown-content ol {
        margin-bottom: 15px;
        padding-left: 30px;
    }

    .markdown-content li {
        margin-bottom: 5px;
    }

    .markdown-content code {
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 0.9em;
        color: #e74c3c;
    }

    .markdown-content pre {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 20px;
        border-radius: 8px;
        overflow-x: auto;
        margin-bottom: 20px;
    }

    .markdown-content pre code {
        background: none;
        color: inherit;
        padding: 0;
        font-size: 0.9em;
    }

    .markdown-content blockquote {
        border-left: 4px solid #3498db;
        padding-left: 20px;
        margin: 20px 0;
        color: #7f8c8d;
        font-style: italic;
    }

    .markdown-content table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .markdown-content th,
    .markdown-content td {
        border: 1px solid #ecf0f1;
        padding: 12px;
        text-align: left;
    }

    .markdown-content th {
        background: #f8f9fa;
        font-weight: bold;
        color: #2c3e50;
    }

    .markdown-content a {
        color: #3498db;
        text-decoration: none;
    }

    .markdown-content a:hover {
        text-decoration: underline;
    }

    .markdown-content img {
        max-width: 100%;
        height: auto;
        border-radius: 5px;
        margin: 10px 0;
    }

    @media (max-width: 768px) {
        .markdown-content {
            padding: 20px;
        }

        .markdown-content h1 {
            font-size: 1.8em;
        }

        .markdown-content h2 {
            font-size: 1.5em;
        }

        .markdown-content pre {
            padding: 15px;
            font-size: 0.8em;
        }
    }
</style>`
}

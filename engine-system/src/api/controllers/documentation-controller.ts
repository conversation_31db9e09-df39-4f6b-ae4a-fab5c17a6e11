import { Request, Response } from 'express'
import { DocumentationService } from '../../services/documentation-service'
import { createErrorResponse } from '../utils/custom.errors'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()
const documentationService = DocumentationService.getInstance()

/**
 * Serve the main documentation index page
 */
export const getDocumentationIndex = (_req: Request, res: Response) => {
    documentationService
        .getDocumentation()
        .then((documentation) => {
            // Group files by directory for better organization
            const groupedFiles = groupFilesByDirectory(documentation.files)

            const html = generateIndexHTML(
                groupedFiles,
                documentation.lastUpdated
            )

            res.setHeader('Content-Type', 'text/html')
            res.status(200).send(html)
        })
        .catch((error) => {
            logger.error('Error serving documentation index:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Serve a specific documentation file
 */
export const getDocumentationFile = (req: Request, res: Response) => {
    // Extract file path from wildcard route
    const filePath = req.params[0] || req.path.replace('/docs/file/', '')

    if (!filePath) {
        return res.status(400).json({ error: 'File path is required' })
    }

    documentationService
        .getDocumentationFile(decodeURIComponent(filePath))
        .then((file) => {
            if (!file) {
                return res
                    .status(404)
                    .json({ error: 'Documentation file not found' })
            }

            const html = generateFileHTML(file)

            res.setHeader('Content-Type', 'text/html')
            res.status(200).send(html)
        })
        .catch((error) => {
            logger.error('Error serving documentation file:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Refresh documentation cache
 */
export const refreshDocumentation = (_req: Request, res: Response) => {
    documentationService.clearCache()
    documentationService
        .getDocumentation(true)
        .then((documentation) => {
            res.status(200).json({
                message: 'Documentation cache refreshed',
                filesCount: documentation.files.length,
                lastUpdated: documentation.lastUpdated,
            })
        })
        .catch((error) => {
            logger.error('Error refreshing documentation:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Get documentation as JSON API
 */
export const getDocumentationAPI = (_req: Request, res: Response) => {
    documentationService
        .getDocumentation()
        .then((documentation) => {
            res.status(200).json(documentation)
        })
        .catch((error) => {
            logger.error('Error serving documentation API:', error)
            return createErrorResponse(error, res)
        })
}

/**
 * Group files by their directory structure
 */
function groupFilesByDirectory(files: any[]) {
    const groups: { [key: string]: any[] } = {}

    files.forEach((file) => {
        const dir = file.path.includes('/') ? file.path.split('/')[0] : 'Root'
        if (!groups[dir]) {
            groups[dir] = []
        }
        groups[dir].push(file)
    })

    // Sort files within each group
    Object.keys(groups).forEach((key) => {
        groups[key].sort((a, b) => a.title.localeCompare(b.title))
    })

    return groups
}

/**
 * Generate HTML for the documentation index page
 */
function generateIndexHTML(
    groupedFiles: { [key: string]: any[] },
    lastUpdated: Date
): string {
    const groups = Object.keys(groupedFiles).sort()

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Engine Documentation</title>
    ${getCommonStyles()}
</head>
<body>
    <div class="container">
        <header>
            <h1>📚 Notification Engine Documentation</h1>
            <p class="subtitle">Comprehensive documentation for the notification engine system</p>
            <div class="meta">
                <span>Last updated: ${lastUpdated.toLocaleString()}</span>
                <a href="/docs/refresh" class="refresh-btn">🔄 Refresh</a>
                <a href="/docs/api" class="api-btn">📋 JSON API</a>
            </div>
        </header>

        <main>
            ${groups
                .map(
                    (group) => `
                <section class="doc-group">
                    <h2>${group === 'Root' ? '📄 Root Documentation' : `📁 ${group}`}</h2>
                    <div class="file-grid">
                        ${groupedFiles[group]
                            .map(
                                (file) => `
                            <div class="file-card">
                                <h3><a href="/docs/file/${encodeURIComponent(file.path)}">${file.title}</a></h3>
                                <p class="file-path">${file.path}</p>
                                <p class="file-modified">Modified: ${file.lastModified.toLocaleDateString()}</p>
                                <div class="file-preview">${file.htmlContent.substring(0, 200)}...</div>
                                <div class="file-actions">
                                    <a href="/docs/file/${encodeURIComponent(file.path)}" class="view-btn">📖 View</a>
                                </div>
                            </div>
                        `
                            )
                            .join('')}
                    </div>
                </section>
            `
                )
                .join('')}
        </main>

        <footer>
            <p>Generated by Notification Engine Documentation Service</p>
        </footer>
    </div>
</body>
</html>`
}

/**
 * Generate HTML for a specific documentation file
 */
function generateFileHTML(file: any): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${file.title} - Notification Engine Docs</title>
    ${getCommonStyles()}
    ${getMarkdownStyles()}
</head>
<body>
    <div class="container">
        <header>
            <nav class="breadcrumb">
                <a href="/docs">📚 Documentation</a> / <span>${file.title}</span>
            </nav>
            <div class="file-meta">
                <h1>${file.title}</h1>
                <p class="file-info">
                    <span class="file-path">📄 ${file.path}</span>
                    <span class="file-modified">🕒 Modified: ${file.lastModified.toLocaleString()}</span>
                </p>
            </div>
        </header>

        <main class="markdown-content">
            ${file.htmlContent}
        </main>

        <footer>
            <a href="/docs" class="back-btn">← Back to Documentation Index</a>
        </footer>
    </div>
</body>
</html>`
}

/**
 * Common CSS styles
 */
function getCommonStyles(): string {
    return `
<style>
    /* CSS Reset and Base Styles */
    *, *::before, *::after {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    :root {
        /* Color Palette */
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --accent-color: #27ae60;
        --danger-color: #e74c3c;
        --warning-color: #f39c12;
        --success-color: #27ae60;

        /* Neutral Colors */
        --text-primary: #2c3e50;
        --text-secondary: #7f8c8d;
        --text-muted: #95a5a6;
        --border-light: #ecf0f1;
        --border-medium: #bdc3c7;
        --background-primary: #ffffff;
        --background-secondary: #f8f9fa;
        --background-tertiary: #ecf0f1;

        /* Spacing Scale */
        --space-xs: 0.25rem;   /* 4px */
        --space-sm: 0.5rem;    /* 8px */
        --space-md: 1rem;      /* 16px */
        --space-lg: 1.5rem;    /* 24px */
        --space-xl: 2rem;      /* 32px */
        --space-2xl: 3rem;     /* 48px */
        --space-3xl: 4rem;     /* 64px */

        /* Typography Scale */
        --font-size-xs: 0.75rem;   /* 12px */
        --font-size-sm: 0.875rem;  /* 14px */
        --font-size-base: 1rem;    /* 16px */
        --font-size-lg: 1.125rem;  /* 18px */
        --font-size-xl: 1.25rem;   /* 20px */
        --font-size-2xl: 1.5rem;   /* 24px */
        --font-size-3xl: 1.875rem; /* 30px */
        --font-size-4xl: 2.25rem;  /* 36px */
        --font-size-5xl: 3rem;     /* 48px */

        /* Border Radius */
        --radius-sm: 0.25rem;   /* 4px */
        --radius-md: 0.5rem;    /* 8px */
        --radius-lg: 0.75rem;   /* 12px */
        --radius-xl: 1rem;      /* 16px */

        /* Shadows */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

        /* Transitions */
        --transition-fast: 150ms ease-in-out;
        --transition-normal: 250ms ease-in-out;
        --transition-slow: 350ms ease-in-out;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        line-height: 1.6;
        color: var(--text-primary);
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-size: var(--font-size-base);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: var(--space-lg);
    }

    header {
        background: var(--background-primary);
        padding: var(--space-2xl);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-lg);
        margin-bottom: var(--space-2xl);
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
    }

    header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    }

    h1 {
        color: var(--text-primary);
        margin-bottom: var(--space-md);
        font-size: var(--font-size-4xl);
        font-weight: 700;
        letter-spacing: -0.025em;
    }

    .subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-xl);
        margin-bottom: var(--space-lg);
        font-weight: 400;
    }

    .meta {
        display: flex;
        gap: var(--space-md);
        align-items: center;
        flex-wrap: wrap;
        margin-top: var(--space-lg);
        padding-top: var(--space-lg);
        border-top: 1px solid var(--border-light);
    }

    /* Button Styles */
    .refresh-btn, .api-btn, .back-btn {
        background: var(--secondary-color);
        color: white;
        text-decoration: none;
        padding: var(--space-sm) var(--space-md);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        transition: all var(--transition-normal);
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        border: 1px solid transparent;
        cursor: pointer;
        white-space: nowrap;
    }

    .refresh-btn:hover, .api-btn:hover, .back-btn:hover {
        background: #2980b9;
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        text-decoration: none;
        color: white;
    }

    .refresh-btn:active, .api-btn:active, .back-btn:active {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }

    /* Documentation Groups */
    .doc-group {
        background: var(--background-primary);
        padding: var(--space-2xl);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--space-2xl);
        border: 1px solid var(--border-light);
        transition: box-shadow var(--transition-normal);
    }

    .doc-group:hover {
        box-shadow: var(--shadow-lg);
    }

    .doc-group h2 {
        color: var(--text-primary);
        margin-bottom: var(--space-xl);
        padding-bottom: var(--space-md);
        border-bottom: 2px solid var(--border-light);
        font-size: var(--font-size-2xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--space-sm);
    }

    /* File Grid Layout */
    .file-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--space-xl);
        margin-top: var(--space-lg);
    }

    /* File Card Styling */
    .file-card {
        background: var(--background-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        padding: var(--space-xl);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .file-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
        transform: scaleX(0);
        transition: transform var(--transition-normal);
    }

    .file-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--secondary-color);
    }

    .file-card:hover::before {
        transform: scaleX(1);
    }

    .file-card h3 {
        margin-bottom: var(--space-md);
        font-size: var(--font-size-lg);
        font-weight: 600;
        line-height: 1.3;
    }

    .file-card h3 a {
        color: var(--text-primary);
        text-decoration: none;
        transition: color var(--transition-fast);
    }

    .file-card h3 a:hover {
        color: var(--secondary-color);
    }

    .file-path {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        margin-bottom: var(--space-sm);
        background: var(--background-secondary);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-sm);
        display: inline-block;
    }

    .file-modified {
        color: var(--text-muted);
        font-size: var(--font-size-xs);
        margin-bottom: var(--space-md);
        display: flex;
        align-items: center;
        gap: var(--space-xs);
    }

    .file-modified::before {
        content: '🕒';
        font-size: var(--font-size-sm);
    }

    .file-preview {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        line-height: 1.5;
        flex-grow: 1;
        margin: var(--space-md) 0;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
    }

    .file-actions {
        margin-top: auto;
        padding-top: var(--space-md);
        border-top: 1px solid var(--border-light);
        display: flex;
        gap: var(--space-sm);
        align-items: center;
    }

    .view-btn {
        background: var(--accent-color);
        color: white;
        text-decoration: none;
        padding: var(--space-sm) var(--space-md);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        transition: all var(--transition-normal);
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        border: none;
        cursor: pointer;
    }

    .view-btn:hover {
        background: #229954;
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        text-decoration: none;
        color: white;
    }

    .view-btn:active {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }

    /* Breadcrumb Navigation */
    .breadcrumb {
        margin-bottom: var(--space-xl);
        padding: var(--space-md);
        background: var(--background-primary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-light);
        font-size: var(--font-size-sm);
    }

    .breadcrumb a {
        color: var(--secondary-color);
        text-decoration: none;
        transition: color var(--transition-fast);
        font-weight: 500;
    }

    .breadcrumb a:hover {
        color: var(--primary-color);
        text-decoration: underline;
    }

    .breadcrumb span {
        color: var(--text-muted);
        margin: 0 var(--space-sm);
    }

    /* File Meta Information */
    .file-meta h1 {
        margin-bottom: var(--space-md);
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
    }

    .file-info {
        display: flex;
        gap: var(--space-lg);
        flex-wrap: wrap;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        align-items: center;
        margin-bottom: var(--space-lg);
        padding: var(--space-md);
        background: var(--background-secondary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-light);
    }

    .file-info > span {
        display: flex;
        align-items: center;
        gap: var(--space-xs);
    }

    /* Footer Styling */
    footer {
        text-align: center;
        margin-top: var(--space-3xl);
        padding: var(--space-xl);
        color: var(--text-muted);
        background: var(--background-primary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-light);
        font-size: var(--font-size-sm);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .file-grid {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--space-lg);
        }
    }

    @media (max-width: 768px) {
        .container {
            padding: var(--space-md);
        }

        header {
            padding: var(--space-xl);
        }

        h1 {
            font-size: var(--font-size-3xl);
        }

        .subtitle {
            font-size: var(--font-size-lg);
        }

        .file-grid {
            grid-template-columns: 1fr;
            gap: var(--space-lg);
        }

        .meta {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-sm);
        }

        .file-info {
            flex-direction: column;
            gap: var(--space-sm);
            align-items: flex-start;
        }

        .doc-group {
            padding: var(--space-xl);
        }

        .doc-group h2 {
            font-size: var(--font-size-xl);
        }

        .file-card {
            padding: var(--space-lg);
        }

        .refresh-btn, .api-btn, .back-btn, .view-btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: var(--space-sm);
        }

        header {
            padding: var(--space-lg);
        }

        h1 {
            font-size: var(--font-size-2xl);
        }

        .subtitle {
            font-size: var(--font-size-base);
        }

        .doc-group {
            padding: var(--space-lg);
        }

        .file-card {
            padding: var(--space-md);
        }

        .file-actions {
            flex-direction: column;
            gap: var(--space-sm);
        }
    }

    /* Focus and Accessibility */
    .refresh-btn:focus, .api-btn:focus, .back-btn:focus, .view-btn:focus,
    .file-card h3 a:focus, .breadcrumb a:focus {
        outline: 2px solid var(--secondary-color);
        outline-offset: 2px;
    }

    /* Print Styles */
    @media print {
        body {
            background: white;
        }

        .container {
            max-width: none;
            padding: 0;
        }

        header, .doc-group, .file-card {
            box-shadow: none;
            border: 1px solid #ccc;
        }

        .refresh-btn, .api-btn, .back-btn, .view-btn {
            display: none;
        }
    }
</style>`
}

/**
 * Markdown-specific CSS styles
 */
function getMarkdownStyles(): string {
    return `
<style>
    /* Markdown Content Container */
    .markdown-content {
        background: var(--background-primary);
        padding: var(--space-3xl);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-lg);
        margin-bottom: var(--space-2xl);
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
    }

    .markdown-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    }

    /* Typography Hierarchy */
    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3,
    .markdown-content h4,
    .markdown-content h5,
    .markdown-content h6 {
        color: var(--text-primary);
        margin-top: var(--space-2xl);
        margin-bottom: var(--space-lg);
        line-height: 1.3;
        font-weight: 600;
        letter-spacing: -0.025em;
    }

    .markdown-content h1:first-child,
    .markdown-content h2:first-child,
    .markdown-content h3:first-child {
        margin-top: 0;
    }

    .markdown-content h1 {
        font-size: var(--font-size-4xl);
        border-bottom: 3px solid var(--secondary-color);
        padding-bottom: var(--space-md);
        font-weight: 700;
    }

    .markdown-content h2 {
        font-size: var(--font-size-3xl);
        border-bottom: 2px solid var(--border-light);
        padding-bottom: var(--space-sm);
        position: relative;
    }

    .markdown-content h2::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 60px;
        height: 2px;
        background: var(--secondary-color);
    }

    .markdown-content h3 {
        font-size: var(--font-size-2xl);
        color: var(--secondary-color);
    }

    .markdown-content h4 {
        font-size: var(--font-size-xl);
    }

    .markdown-content h5 {
        font-size: var(--font-size-lg);
    }

    .markdown-content h6 {
        font-size: var(--font-size-base);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        color: var(--text-secondary);
    }

    /* Text Content */
    .markdown-content p {
        margin-bottom: var(--space-lg);
        line-height: 1.7;
        color: var(--text-primary);
    }

    .markdown-content p:last-child {
        margin-bottom: 0;
    }

    /* Lists */
    .markdown-content ul,
    .markdown-content ol {
        margin-bottom: var(--space-lg);
        padding-left: var(--space-2xl);
    }

    .markdown-content li {
        margin-bottom: var(--space-sm);
        line-height: 1.6;
    }

    .markdown-content li::marker {
        color: var(--secondary-color);
    }

    .markdown-content ul ul,
    .markdown-content ol ol,
    .markdown-content ul ol,
    .markdown-content ol ul {
        margin-top: var(--space-sm);
        margin-bottom: var(--space-sm);
    }

    /* Code Styling */
    .markdown-content code {
        background: var(--background-secondary);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-sm);
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        font-size: 0.875em;
        color: var(--danger-color);
        border: 1px solid var(--border-light);
        font-weight: 500;
    }

    .markdown-content pre {
        background: var(--primary-color);
        color: #ecf0f1;
        padding: var(--space-xl);
        border-radius: var(--radius-lg);
        overflow-x: auto;
        margin: var(--space-xl) 0;
        border: 1px solid var(--border-medium);
        position: relative;
        box-shadow: var(--shadow-md);
    }

    .markdown-content pre::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    }

    .markdown-content pre code {
        background: none;
        color: inherit;
        padding: 0;
        border: none;
        font-size: var(--font-size-sm);
        font-weight: 400;
    }

    .markdown-content pre::-webkit-scrollbar {
        height: 8px;
    }

    .markdown-content pre::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

    .markdown-content pre::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
    }

    .markdown-content pre::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    /* Blockquotes */
    .markdown-content blockquote {
        border-left: 4px solid var(--secondary-color);
        padding: var(--space-lg) var(--space-xl);
        margin: var(--space-xl) 0;
        background: var(--background-secondary);
        border-radius: 0 var(--radius-md) var(--radius-md) 0;
        color: var(--text-secondary);
        font-style: italic;
        position: relative;
    }

    .markdown-content blockquote::before {
        content: '"';
        position: absolute;
        top: var(--space-sm);
        left: var(--space-md);
        font-size: var(--font-size-3xl);
        color: var(--secondary-color);
        opacity: 0.3;
        font-family: serif;
    }

    .markdown-content blockquote p {
        margin-bottom: var(--space-sm);
    }

    .markdown-content blockquote p:last-child {
        margin-bottom: 0;
    }

    /* Tables */
    .markdown-content table {
        width: 100%;
        border-collapse: collapse;
        margin: var(--space-xl) 0;
        background: var(--background-primary);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-light);
    }

    .markdown-content th,
    .markdown-content td {
        padding: var(--space-md) var(--space-lg);
        text-align: left;
        border-bottom: 1px solid var(--border-light);
    }

    .markdown-content th {
        background: var(--background-secondary);
        font-weight: 600;
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .markdown-content tr:hover {
        background: var(--background-secondary);
    }

    .markdown-content tr:last-child td {
        border-bottom: none;
    }

    /* Links */
    .markdown-content a {
        color: var(--secondary-color);
        text-decoration: none;
        transition: all var(--transition-fast);
        border-bottom: 1px solid transparent;
    }

    .markdown-content a:hover {
        color: var(--primary-color);
        border-bottom-color: var(--secondary-color);
    }

    .markdown-content a:focus {
        outline: 2px solid var(--secondary-color);
        outline-offset: 2px;
        border-radius: var(--radius-sm);
    }

    /* Images */
    .markdown-content img {
        max-width: 100%;
        height: auto;
        border-radius: var(--radius-lg);
        margin: var(--space-xl) 0;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-light);
    }

    /* Horizontal Rules */
    .markdown-content hr {
        border: none;
        height: 2px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
        margin: var(--space-2xl) 0;
        border-radius: 1px;
    }

    /* Strong and Emphasis */
    .markdown-content strong {
        font-weight: 600;
        color: var(--text-primary);
    }

    .markdown-content em {
        font-style: italic;
        color: var(--text-secondary);
    }

    /* Responsive Markdown Styles */
    @media (max-width: 768px) {
        .markdown-content {
            padding: var(--space-xl);
        }

        .markdown-content h1 {
            font-size: var(--font-size-3xl);
        }

        .markdown-content h2 {
            font-size: var(--font-size-2xl);
        }

        .markdown-content h3 {
            font-size: var(--font-size-xl);
        }

        .markdown-content pre {
            padding: var(--space-lg);
            margin: var(--space-lg) 0;
        }

        .markdown-content table {
            font-size: var(--font-size-sm);
        }

        .markdown-content th,
        .markdown-content td {
            padding: var(--space-sm);
        }

        .markdown-content blockquote {
            padding: var(--space-md) var(--space-lg);
            margin: var(--space-lg) 0;
        }
    }

    @media (max-width: 480px) {
        .markdown-content {
            padding: var(--space-lg);
        }

        .markdown-content h1 {
            font-size: var(--font-size-2xl);
        }

        .markdown-content h2 {
            font-size: var(--font-size-xl);
        }

        .markdown-content h3 {
            font-size: var(--font-size-lg);
        }

        .markdown-content ul,
        .markdown-content ol {
            padding-left: var(--space-lg);
        }

        .markdown-content pre {
            padding: var(--space-md);
            font-size: var(--font-size-xs);
        }

        .markdown-content table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }
    }

    /* Dark mode support (future enhancement) */
    @media (prefers-color-scheme: dark) {
        :root {
            --text-primary: #ecf0f1;
            --text-secondary: #bdc3c7;
            --text-muted: #95a5a6;
            --background-primary: #2c3e50;
            --background-secondary: #34495e;
            --background-tertiary: #3e5771;
            --border-light: #4a5f7a;
            --border-medium: #5d6d7e;
        }

        body {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
    }
</style>`
}
